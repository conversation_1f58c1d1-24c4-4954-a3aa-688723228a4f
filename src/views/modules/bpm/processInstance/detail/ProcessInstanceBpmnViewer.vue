<template>
  <el-card v-loading="loading" class="box1-card" hoverable>
    <template #header>
      <div style="display: flex; align-items: center; justify-content: space-between">
        <span style="font-size: 20.2px; display: flex; align-items: center">
          <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
            <path
              fill="none"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M21 6.5a3.5 3.5 0 1 1-7 0a3.5 3.5 0 0 1 7 0m-18 0c0-1.65 0-2.475.513-2.987C4.025 3 4.85 3 6.5 3s2.475 0 2.987.513C10 4.025 10 4.85 10 6.5s0 2.475-.513 2.987C8.975 10 8.15 10 6.5 10s-2.475 0-2.987-.513C3 8.975 3 8.15 3 6.5m0 11c0-1.65 0-2.475.513-2.987C4.025 14 4.85 14 6.5 14s2.475 0 2.987.513C10 15.025 10 15.85 10 17.5s0 2.475-.513 2.987C8.975 21 8.15 21 6.5 21s-2.475 0-2.987-.513C3 19.975 3 19.15 3 17.5m11 0c0-1.65 0-2.475.513-2.987C15.025 14 15.85 14 17.5 14s2.475 0 2.987.513C21 15.025 21 15.85 21 17.5s0 2.475-.513 2.987C19.975 21 19.15 21 17.5 21s-2.475 0-2.987-.513C14 19.975 14 19.15 14 17.5m3.5-7.5v4M14 17.5h-4m0-11h4"
              color="currentColor"
            />
          </svg>
          <span style="margin-left: 5px">流程图</span>
        </span>

        <!-- 移动端放大按钮 -->
        <el-button
          v-if="isMobileDevice"
          type="primary"
          size="small"
          @click="showMobileDialog"
          style="display: flex; align-items: center; gap: 4px"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            :fill="dialogVisible ? 'var(--el-color-info)' : 'currentColor'"
          >
            <path
              v-if="dialogVisible"
              d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
            />
            <path
              v-else
              d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
            />
            <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z" />
          </svg>
          点击查看
        </el-button>
      </div>
    </template>

    <MyProcessViewer
      key="designer"
      v-if="!isMobileDevice"
      :activityData="activityList"
      :prefix="bpmnControlForm.prefix"
      :processInstanceData="processInstance"
      :taskData="tasks"
      :value="bpmnXml"
      v-bind="bpmnControlForm"
      @nextTasks="handleNextTasks"
    />
  </el-card>

  <!-- 移动端全屏流程图弹窗 -->
  <n-modal
    v-if="isMobileDevice"
    class="mobile-process-dialog"
    v-model:show="dialogVisible"

    preset="card"

    style="height: 100vw;width:100vh;"
  >
      <MyProcessViewer
        class="mobile-process-container"
        key="mobile-designer"
        :activityData="activityList"
        :prefix="bpmnControlForm.prefix"
        :processInstanceData="processInstance"
        :taskData="tasks"
        :value="bpmnXml"
        v-bind="bpmnControlForm"
        @nextTasks="handleNextTasks"
      />
  </n-modal>
</template>
<script lang="ts" setup>
  import { propTypes } from '@/utils/bpmAdapter/propTypes'
  import { MyProcessViewer } from '@/components/common/bpm/bpmnProcessDesigner/package'
  import * as ActivityApi from '@/api/bpm/activity'
  import { ref, watch, inject, type Ref } from 'vue'

  defineOptions({ name: 'BpmProcessInstanceBpmnViewer' })

  const props = defineProps({
    loading: propTypes.bool, // 是否加载中
    id: propTypes.string, // 流程实例的编号
    processInstance: propTypes.any, // 流程实例的信息
    tasks: propTypes.array, // 流程任务的数组
    bpmnXml: propTypes.string, // BPMN XML
  })

  const emit = defineEmits(['update:nextTasks'])

  // 移动端检测
  const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

  // 弹窗状态管理
  const dialogVisible = ref(false)

  const bpmnControlForm = ref({
    prefix: 'flowable',
  })
  const activityList = ref([]) // 任务列表

  // 处理下一审批任务
  const handleNextTasks = (tasks: any) => {
    console.log('tasks', tasks)
    emit('update:nextTasks', tasks)
  }

  const awaitShow = ref(false)
  const showMobileDialog = async () => {
    dialogVisible.value = true

    await nextTick()
    activityList.value = await ActivityApi.getActivityList({
      processInstanceId: props.id,
    })
    awaitShow.value = true
  }

  /** 只有 loading 完成时，才去加载流程列表 */
  watch(
    () => [props.loading, props.id, props.bpmnXml],
    async value => {
      if (value && props.id) {
        activityList.value = await ActivityApi.getActivityList({
          processInstanceId: props.id,
        })
      }
    }
  )
</script>
<style>
  .box-card {
    width: 100%;
    margin-bottom: 10px;
  }

  /* 移动端流程图弹窗样式 */
  .mobile-process-dialog {
    /* 确保全屏显示 */
    :deep(.el-dialog) {
      margin: 0;

      border-radius: 0;
    }

    :deep(.el-dialog__header) {
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;
      background: #fff;
    }

    :deep(.el-dialog__body) {
      padding: 0;
      height: calc(100vh - 60px);
      overflow: hidden;
    }
  }

  .mobile-process-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vh;
    height: 100vw;
    transform: rotate(90deg);
    transform-origin: center;
    margin-left: calc((100vw - 100vh) / 2);
    margin-top: calc((100vh - 100vw) / 2);
  }

  /* 移动端放大按钮样式 */
  @media (max-width: 768px) {
    .box-card {
      :deep(.el-card__header) {
        padding: 12px 16px;
      }

      :deep(.el-card__body) {
        padding: 12px;
      }
    }
  }
  .mobile-process-dialog {
    z-index: 100000;
  }
</style>
